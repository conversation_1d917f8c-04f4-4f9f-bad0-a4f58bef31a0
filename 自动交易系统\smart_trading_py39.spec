# -*- mode: python ; coding: utf-8 -*-
"""
智能交易系统 - Python 3.9专用配置
"""

import sys
import os

current_dir = os.path.dirname(os.path.abspath(SPEC))

# 数据文件
datas = [
    ('config', 'config'),
    ('core', 'core'),
    ('utils', 'utils')
]

# 确保使用Python 3.9
if sys.version_info >= (3, 10):
    print("警告: 建议使用Python 3.9以获得最佳兼容性")

# 图标
icon_path = os.path.join(current_dir, 'icon.ico')
if not os.path.exists(icon_path):
    icon_path = None

# Python 3.9兼容的隐藏导入
hiddenimports = [
    'PyQt5.QtCore',
    'PyQt5.QtGui', 
    'PyQt5.QtWidgets',
    'PyQt5.QtWidgets.QApplication',
    'PyQt5.QtWidgets.QMainWindow',
    'PyQt5.QtWidgets.QWidget',
    'PyQt5.QtWidgets.QVBoxLayout',
    'PyQt5.QtWidgets.QHBoxLayout',
    'PyQt5.QtWidgets.QPushButton',
    'PyQt5.QtWidgets.QLabel',
    'PyQt5.QtWidgets.QTextEdit',
    'PyQt5.QtWidgets.QCheckBox',
    'PyQt5.QtWidgets.QSpinBox',
    'PyQt5.QtWidgets.QDoubleSpinBox',
    'PyQt5.QtWidgets.QGroupBox',
    'PyQt5.QtWidgets.QMessageBox',
    'PyQt5.QtCore.QThread',
    'PyQt5.QtCore.pyqtSignal',
    'PyQt5.QtCore.QTimer',
    'numpy',
    'numpy.core',
    'numpy.core.multiarray',
    'numpy.core.numeric',
    'numpy.core.umath',
    'numpy.linalg',
    'numpy.random',
    'cv2',
    'PIL',
    'PIL.Image',
    'PIL.ImageDraw',
    'PIL.ImageFont',
    'pyautogui',
    'pytesseract',
    'psutil',
    'json',
    'logging',
    'time',
    'threading',
    'sqlite3',
    'pathlib',
    'collections',
    'typing',
    'core.enhanced_detection',
    'core.enhanced_ocr',
    'core.risk_manager',
    'core.performance_optimizer',
    'core.data_recorder',
    'core.error_handler',
    'core.smart_trading_engine',
    'core.direct_client_controller',
    'utils.resource_path',
    'utils.config_manager',
    'utils.coordinate_calibrator'
]

# 分析
a = Analysis(
    ['smart_trading.py'],
    pathex=[current_dir],
    binaries=[],
    datas=datas,
    hiddenimports=hiddenimports,
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[
        'matplotlib',
        'pandas',
        'scipy',
        'IPython',
        'jupyter',
        'notebook',
        'sphinx',
        'pytest',
        'setuptools',
        'distutils',
        'tkinter.test',
        'test',
        'unittest'
    ],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=None,
    noarchive=False,
)

# 创建PYZ
pyz = PYZ(a.pure, a.zipped_data, cipher=None)

# 创建目录版EXE（更好的兼容性）
exe = EXE(
    pyz,
    a.scripts,
    [],
    exclude_binaries=True,
    name='智能交易系统',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=icon_path,
    version='version_info.txt'
)

# 目录版本
coll = COLLECT(
    exe,
    a.binaries,
    a.zipfiles,
    a.datas,
    strip=False,
    upx=False,
    upx_exclude=[],
    name='智能交易系统_Python39版'
)
