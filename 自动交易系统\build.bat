@echo off
chcp 65001 >nul
echo ========================================
echo 智能交易系统打包脚本
echo ========================================
echo.

:: 设置变量
set PYTHON_PATH="C:\Program Files\Python39\python.exe"
set PROJECT_NAME=智能交易系统
set BUILD_DIR=build
set DIST_DIR=dist

:: 检查Python环境
echo [1/6] 检查Python环境...
%PYTHON_PATH% --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python环境
    echo 请确保Python已安装并添加到PATH环境变量
    pause
    exit /b 1
)
echo ✅ Python环境检查通过

:: 检查必要文件
echo.
echo [2/6] 检查必要文件...
if not exist "smart_trading.py" (
    echo ❌ 错误: 未找到主程序文件 smart_trading.py
    pause
    exit /b 1
)
if not exist "smart_trading_py39.spec" (
    echo ❌ 错误: 未找到配置文件 smart_trading_py39.spec
    pause
    exit /b 1
)
echo ✅ 必要文件检查通过

:: 安装依赖
echo.
echo [3/6] 安装/更新依赖包...
echo 正在安装PyInstaller...
%PYTHON_PATH% -m pip install pyinstaller --upgrade
if errorlevel 1 (
    echo ❌ 错误: PyInstaller安装失败
    pause
    exit /b 1
)

echo 正在安装其他依赖...
%PYTHON_PATH% -m pip install -r requirements.txt
if errorlevel 1 (
    echo ⚠️ 警告: 部分依赖安装失败，但可能不影响打包
)
echo ✅ 依赖安装完成

:: 清理旧的构建文件
echo.
echo [4/6] 清理旧的构建文件...
if exist "%BUILD_DIR%" (
    echo 删除旧的build目录...
    rmdir /s /q "%BUILD_DIR%"
)
if exist "%DIST_DIR%" (
    echo 删除旧的dist目录...
    rmdir /s /q "%DIST_DIR%"
)
if exist "*.pyc" (
    echo 删除Python缓存文件...
    del /q *.pyc
)
echo ✅ 清理完成

:: 开始打包
echo.
echo [5/6] 开始打包程序...
echo 这可能需要几分钟时间，请耐心等待...
echo.

%PYTHON_PATH% -m PyInstaller smart_trading_py39.spec --clean --noconfirm

if errorlevel 1 (
    echo.
    echo ❌ 打包失败！
    echo 请检查错误信息并修复问题后重试
    pause
    exit /b 1
)

:: 检查打包结果
echo.
echo [6/6] 检查打包结果...
if exist "dist\%PROJECT_NAME%.exe" (
    echo ✅ 单文件版本打包成功！
    echo 📁 位置: dist\%PROJECT_NAME%.exe
) else (
    echo ❌ 单文件版本打包失败
)

if exist "dist\%PROJECT_NAME%_目录版" (
    echo ✅ 目录版本打包成功！
    echo 📁 位置: dist\%PROJECT_NAME%_目录版\
) else (
    echo ❌ 目录版本打包失败
)

:: 显示文件大小
echo.
echo 📊 打包结果统计:
if exist "dist\%PROJECT_NAME%.exe" (
    for %%I in ("dist\%PROJECT_NAME%.exe") do echo 单文件版本大小: %%~zI 字节
)

:: 创建快捷方式脚本
echo.
echo [额外] 创建桌面快捷方式脚本...
echo Set oWS = WScript.CreateObject("WScript.Shell") > create_shortcut.vbs
echo sLinkFile = oWS.ExpandEnvironmentStrings("%%USERPROFILE%%\Desktop\%PROJECT_NAME%.lnk") >> create_shortcut.vbs
echo Set oLink = oWS.CreateShortcut(sLinkFile) >> create_shortcut.vbs
echo oLink.TargetPath = "%CD%\dist\%PROJECT_NAME%.exe" >> create_shortcut.vbs
echo oLink.WorkingDirectory = "%CD%\dist" >> create_shortcut.vbs
echo oLink.Description = "智能交易系统" >> create_shortcut.vbs
echo oLink.Save >> create_shortcut.vbs

echo.
echo ========================================
echo 🎉 打包完成！
echo ========================================
echo.
echo 📁 打包文件位置:
echo    - 单文件版: dist\%PROJECT_NAME%.exe
echo    - 目录版:   dist\%PROJECT_NAME%_目录版\
echo.
echo 💡 使用建议:
echo    - 单文件版: 适合分发给其他用户
echo    - 目录版:   启动速度更快，适合自己使用
echo.
echo 🔧 如需创建桌面快捷方式，请运行:
echo    create_shortcut.vbs
echo.
echo 按任意键退出...
pause >nul
