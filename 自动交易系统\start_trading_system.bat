@echo off
chcp 65001
echo 智能交易系统启动器
echo ===================

echo 检查环境...
python -c "import PyQt5, cv2, pyautogui, numpy" >nul 2>&1
if errorlevel 1 (
    echo 依赖包未安装，正在修复环境...
    call fix_environment.bat
    if errorlevel 1 (
        echo 环境修复失败，请手动安装依赖包
        pause
        exit /b 1
    )
)

echo 启动智能交易系统...
echo 注意：请确保景陶易购客户端已经打开
echo.

set QT_QPA_PLATFORM_PLUGIN_PATH=
python smart_trading.py

if errorlevel 1 (
    echo.
    echo 程序运行出错，请检查：
    echo 1. 景陶易购客户端是否已打开
    echo 2. Python依赖包是否正确安装
    echo 3. 查看上方错误信息
    echo.
)

pause
