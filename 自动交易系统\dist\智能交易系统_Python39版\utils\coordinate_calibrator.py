#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
坐标校准工具
帮助用户手动调整按钮位置
"""

import tkinter as tk
from tkinter import ttk, messagebox
import pyautogui
import json
import os
from typing import Dict, Tuple

class CoordinateCalibrator:
    """坐标校准器"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("智能交易系统 - 坐标校准工具")
        self.root.geometry("600x500")
        
        # 按钮配置
        self.buttons = {
            'buy_mode_button': {'name': '买入模式按钮', 'x': 0.1, 'y': 0.3},  # 左侧垂直买入按钮
            'sell_mode_button': {'name': '卖出模式按钮', 'x': 0.1, 'y': 0.4},  # 左侧垂直卖出按钮
            'buy_order_button': {'name': '买入订立按钮', 'x': 0.35, 'y': 0.85},  # 底部买入订立按钮
            'sell_order_button': {'name': '卖出订立按钮', 'x': 0.35, 'y': 0.85},  # 底部卖出订立按钮
            'price_input': {'name': '价格输入框', 'x': 0.25, 'y': 0.7},  # 买价输入框
            'quantity_input': {'name': '数量输入框', 'x': 0.25, 'y': 0.75},  # 买量输入框
            'confirm_button': {'name': '确认按钮', 'x': 0.5, 'y': 0.7},  # 确认按钮
            'transfer_out_button': {'name': '转出按钮', 'x': 0.6, 'y': 0.8}  # 转出按钮
        }
        
        self.current_button = None
        self.client_window = None
        self.window_rect = None
        
        self.setup_ui()
        self.find_client_window()
    
    def setup_ui(self):
        """设置用户界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 标题
        title_label = ttk.Label(main_frame, text="坐标校准工具", font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 说明
        info_text = """
        使用说明：
        1. 确保景陶易购客户端已打开
        2. 点击"查找客户端窗口"按钮
        3. 选择要校准的按钮
        4. 点击"获取鼠标位置"获取当前鼠标坐标
        5. 点击"测试点击"验证位置是否正确
        6. 点击"保存配置"保存校准结果
        """
        info_label = ttk.Label(main_frame, text=info_text, justify=tk.LEFT)
        info_label.grid(row=1, column=0, columnspan=3, pady=(0, 20))
        
        # 客户端窗口信息
        self.window_info_label = ttk.Label(main_frame, text="未找到客户端窗口")
        self.window_info_label.grid(row=2, column=0, columnspan=3, pady=(0, 10))
        
        # 查找窗口按钮
        find_button = ttk.Button(main_frame, text="查找客户端窗口", command=self.find_client_window)
        find_button.grid(row=3, column=0, columnspan=3, pady=(0, 20))
        
        # 按钮选择框架
        button_frame = ttk.LabelFrame(main_frame, text="选择要校准的按钮", padding="10")
        button_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 20))
        
        # 按钮选择下拉框
        self.button_var = tk.StringVar()
        button_names = [f"{info['name']} ({key})" for key, info in self.buttons.items()]
        self.button_combo = ttk.Combobox(button_frame, textvariable=self.button_var, values=button_names, state="readonly")
        self.button_combo.grid(row=0, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=(0, 10))
        self.button_combo.bind('<<ComboboxSelected>>', self.on_button_selected)
        
        # 坐标显示框架
        coord_frame = ttk.LabelFrame(main_frame, text="坐标信息", padding="10")
        coord_frame.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 20))
        
        # 相对坐标
        ttk.Label(coord_frame, text="相对坐标 (0-1):").grid(row=0, column=0, sticky=tk.W)
        self.rel_x_var = tk.StringVar(value="0.1")
        self.rel_y_var = tk.StringVar(value="0.3")
        ttk.Entry(coord_frame, textvariable=self.rel_x_var, width=10).grid(row=0, column=1, padx=(5, 0))
        ttk.Entry(coord_frame, textvariable=self.rel_y_var, width=10).grid(row=0, column=2, padx=(5, 0))
        
        # 绝对坐标
        ttk.Label(coord_frame, text="绝对坐标 (像素):").grid(row=1, column=0, sticky=tk.W, pady=(10, 0))
        self.abs_x_var = tk.StringVar(value="0")
        self.abs_y_var = tk.StringVar(value="0")
        ttk.Entry(coord_frame, textvariable=self.abs_x_var, width=10).grid(row=1, column=1, padx=(5, 0), pady=(10, 0))
        ttk.Entry(coord_frame, textvariable=self.abs_y_var, width=10).grid(row=1, column=2, padx=(5, 0), pady=(10, 0))
        
        # 操作按钮框架
        action_frame = ttk.Frame(main_frame)
        action_frame.grid(row=6, column=0, columnspan=3, pady=(0, 20))
        
        # 获取鼠标位置按钮
        get_pos_button = ttk.Button(action_frame, text="获取鼠标位置", command=self.get_mouse_position)
        get_pos_button.grid(row=0, column=0, padx=(0, 10))
        
        # 测试点击按钮
        test_click_button = ttk.Button(action_frame, text="测试点击", command=self.test_click)
        test_click_button.grid(row=0, column=1, padx=(0, 10))
        
        # 保存配置按钮
        save_button = ttk.Button(action_frame, text="保存配置", command=self.save_config)
        save_button.grid(row=0, column=2, padx=(0, 10))
        
        # 重置按钮
        reset_button = ttk.Button(action_frame, text="重置", command=self.reset_config)
        reset_button.grid(row=0, column=3)
        
        # 状态标签
        self.status_label = ttk.Label(main_frame, text="就绪")
        self.status_label.grid(row=7, column=0, columnspan=3, pady=(10, 0))
    
    def find_client_window(self):
        """查找客户端窗口"""
        try:
            self.status_label.config(text="正在查找客户端窗口...")
            self.root.update()
            
            # 通过窗口标题查找
            windows = pyautogui.getWindowsWithTitle("景陶易购")
            if windows:
                self.client_window = windows[0]
                try:
                    self.window_rect = self.client_window.rect
                except AttributeError:
                    # 创建自定义的rect对象
                    from collections import namedtuple
                    Rect = namedtuple('Rect', ['left', 'top', 'width', 'height', 'right', 'bottom'])
                    self.window_rect = Rect(
                        left=self.client_window.left,
                        top=self.client_window.top,
                        width=self.client_window.width,
                        height=self.client_window.height,
                        right=self.client_window.left + self.client_window.width,
                        bottom=self.client_window.top + self.client_window.height
                    )
                self.window_info_label.config(text=f"找到窗口: {self.client_window.title} - 位置: {self.window_rect}")
                self.status_label.config(text="客户端窗口已找到")
                return True
            
            # 通过关键词查找
            all_windows = pyautogui.getAllWindows()
            self.status_label.config(text=f"正在搜索 {len(all_windows)} 个窗口...")
            self.root.update()
            
            for window in all_windows:
                window_title = window.title
                if any(keyword in window_title for keyword in ['景陶易购', 'DEAL', 'JCST', 'RNHY']):
                    self.client_window = window
                    try:
                        self.window_rect = self.client_window.rect
                    except AttributeError:
                        # 创建自定义的rect对象
                        from collections import namedtuple
                        Rect = namedtuple('Rect', ['left', 'top', 'width', 'height', 'right', 'bottom'])
                        self.window_rect = Rect(
                            left=self.client_window.left,
                            top=self.client_window.top,
                            width=self.client_window.width,
                            height=self.client_window.height,
                            right=self.client_window.left + self.client_window.width,
                            bottom=self.client_window.top + self.client_window.height
                        )
                    self.window_info_label.config(text=f"找到窗口: {window.title} - 位置: {self.window_rect}")
                    self.status_label.config(text="客户端窗口已找到")
                    return True
            
            # 显示所有窗口标题用于调试
            window_titles = [w.title for w in all_windows]
            debug_info = f"未找到客户端窗口。可用窗口: {', '.join(window_titles[:5])}..."
            self.window_info_label.config(text=debug_info)
            self.status_label.config(text="未找到客户端窗口")
            return False
            
        except Exception as e:
            self.status_label.config(text=f"查找窗口失败: {e}")
            return False
    
    def on_button_selected(self, event):
        """按钮选择事件"""
        try:
            selected = self.button_var.get()
            if selected:
                # 提取按钮键名
                button_key = selected.split('(')[1].split(')')[0]
                self.current_button = button_key
                
                # 显示当前坐标
                if button_key in self.buttons:
                    rel_x = self.buttons[button_key]['x']
                    rel_y = self.buttons[button_key]['y']
                    self.rel_x_var.set(f"{rel_x:.3f}")
                    self.rel_y_var.set(f"{rel_y:.3f}")
                    
                    # 计算绝对坐标
                    if self.window_rect:
                        abs_x = int(self.window_rect.left + self.window_rect.width * rel_x)
                        abs_y = int(self.window_rect.top + self.window_rect.height * rel_y)
                        self.abs_x_var.set(str(abs_x))
                        self.abs_y_var.set(str(abs_y))
                
                self.status_label.config(text=f"已选择: {self.buttons[button_key]['name']}")
            
        except Exception as e:
            self.status_label.config(text=f"选择按钮失败: {e}")
    
    def get_mouse_position(self):
        """获取鼠标位置"""
        try:
            if not self.current_button:
                messagebox.showwarning("警告", "请先选择一个按钮")
                return
            
            # 激活客户端窗口
            if self.client_window:
                self.client_window.activate()
            
            # 等待用户移动鼠标
            self.status_label.config(text="请将鼠标移动到目标位置，3秒后获取坐标...")
            self.root.update()
            
            # 倒计时
            for i in range(3, 0, -1):
                self.status_label.config(text=f"请将鼠标移动到目标位置，{i}秒后获取坐标...")
                self.root.update()
                self.root.after(1000)
            
            # 获取鼠标位置
            x, y = pyautogui.position()
            
            # 计算相对坐标
            if self.window_rect:
                rel_x = (x - self.window_rect.left) / self.window_rect.width
                rel_y = (y - self.window_rect.top) / self.window_rect.height
                
                # 更新显示
                self.abs_x_var.set(str(x))
                self.abs_y_var.set(str(y))
                self.rel_x_var.set(f"{rel_x:.3f}")
                self.rel_y_var.set(f"{rel_y:.3f}")
                
                # 更新配置
                self.buttons[self.current_button]['x'] = rel_x
                self.buttons[self.current_button]['y'] = rel_y
                
                self.status_label.config(text=f"已获取坐标: ({x}, {y}) - 相对位置: ({rel_x:.3f}, {rel_y:.3f})")
            else:
                self.status_label.config(text="无法计算相对坐标，窗口信息不完整")
            
        except Exception as e:
            self.status_label.config(text=f"获取鼠标位置失败: {e}")
    
    def test_click(self):
        """测试点击"""
        try:
            if not self.current_button:
                messagebox.showwarning("警告", "请先选择一个按钮")
                return
            
            # 获取绝对坐标
            abs_x = int(self.abs_x_var.get())
            abs_y = int(self.abs_y_var.get())
            
            # 激活窗口
            if self.client_window:
                self.client_window.activate()
                self.root.after(500)  # 等待窗口激活
            
            # 执行点击
            pyautogui.click(abs_x, abs_y)
            
            self.status_label.config(text=f"已测试点击: ({abs_x}, {abs_y})")
            
        except Exception as e:
            self.status_label.config(text=f"测试点击失败: {e}")
    
    def save_config(self):
        """保存配置"""
        try:
            # 创建配置数据
            window_info = {}
            if self.client_window:
                window_info['title'] = self.client_window.title
                if self.window_rect:
                    try:
                        window_info['rect'] = self.window_rect._asdict()
                    except AttributeError:
                        window_info['rect'] = {
                            'left': self.window_rect.left,
                            'top': self.window_rect.top,
                            'width': self.window_rect.width,
                            'height': self.window_rect.height,
                            'right': self.window_rect.right,
                            'bottom': self.window_rect.bottom
                        }
            
            config_data = {
                'window_info': window_info,
                'button_positions': self.buttons
            }
            
            # 保存到文件
            config_file = "smart_coordinates_config.json"
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, indent=2, ensure_ascii=False)
            
            self.status_label.config(text=f"配置已保存到: {config_file}")
            messagebox.showinfo("成功", f"配置已保存到: {config_file}")
            
        except Exception as e:
            self.status_label.config(text=f"保存配置失败: {e}")
            messagebox.showerror("错误", f"保存配置失败: {e}")
    
    def reset_config(self):
        """重置配置"""
        try:
            # 重置为默认值
            self.buttons = {
                'buy_mode_button': {'name': '买入模式按钮', 'x': 0.1, 'y': 0.3},  # 左侧垂直买入按钮
                'sell_mode_button': {'name': '卖出模式按钮', 'x': 0.1, 'y': 0.4},  # 左侧垂直卖出按钮
                'buy_order_button': {'name': '买入订立按钮', 'x': 0.35, 'y': 0.85},  # 底部买入订立按钮
                'sell_order_button': {'name': '卖出订立按钮', 'x': 0.35, 'y': 0.85},  # 底部卖出订立按钮
                'price_input': {'name': '价格输入框', 'x': 0.25, 'y': 0.7},  # 买价输入框
                'quantity_input': {'name': '数量输入框', 'x': 0.25, 'y': 0.75},  # 买量输入框
                'confirm_button': {'name': '确认按钮', 'x': 0.5, 'y': 0.7},  # 确认按钮
                'transfer_out_button': {'name': '转出按钮', 'x': 0.6, 'y': 0.8}  # 转出按钮
            }
            
            # 重置显示
            if self.current_button and self.current_button in self.buttons:
                rel_x = self.buttons[self.current_button]['x']
                rel_y = self.buttons[self.current_button]['y']
                self.rel_x_var.set(f"{rel_x:.3f}")
                self.rel_y_var.set(f"{rel_y:.3f}")
                
                if self.window_rect:
                    abs_x = int(self.window_rect.left + self.window_rect.width * rel_x)
                    abs_y = int(self.window_rect.top + self.window_rect.height * rel_y)
                    self.abs_x_var.set(str(abs_x))
                    self.abs_y_var.set(str(abs_y))
            
            self.status_label.config(text="配置已重置")
            
        except Exception as e:
            self.status_label.config(text=f"重置配置失败: {e}")
    
    def run(self):
        """运行校准工具"""
        self.root.mainloop()

def main():
    """主函数"""
    calibrator = CoordinateCalibrator()
    calibrator.run()

if __name__ == "__main__":
    main() 