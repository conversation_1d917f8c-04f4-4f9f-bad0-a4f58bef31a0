#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理模块
负责管理系统配置信息
"""

import json
import logging
import os
from typing import Dict, Any, Optional

class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_file: str = "config.json"):
        self.logger = logging.getLogger(__name__)
        self.config_file = config_file
        self.config = self.load_config()
    
    def load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            if os.path.exists(self.config_file):
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                self.logger.info("配置文件加载成功")
                return config
            else:
                # 创建默认配置
                default_config = self.get_default_config()
                self.save_config(default_config)
                return default_config
                
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            return self.get_default_config()
    
    def save_config(self, config: Dict[str, Any]) -> bool:
        """保存配置文件"""
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            
            self.config = config
            self.logger.info("配置文件保存成功")
            return True
            
        except Exception as e:
            self.logger.error(f"保存配置文件失败: {e}")
            return False
    
    def get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            "system": {
                "log_level": "INFO",
                "log_file": "trading_system.log",
                "max_log_size": 10485760,
                "backup_count": 5
            },
            "trading": {
                "angle_threshold": 20,
                "default_quantity": 1,
                "check_interval": 5,
                "profit_threshold": 1,
                "loss_threshold": -3,
                "max_trades_per_day": 50,
                "max_loss_per_day": 100,
                "enable_continuous_ordering": True,
                "signal_frequency_limit": 5
            },
            "ui": {
                "window_width": 1200,
                "window_height": 800,
                "theme": "dark",
                "language": "zh_CN"
            },
            "recognition": {
                "yellow_line_color_range": {
                    "lower": [15, 100, 100],
                    "upper": [35, 255, 255]
                },
                "ocr_confidence_threshold": 0.8,
                "image_preprocessing": {
                    "scale_factor": 2,
                    "denoise": True,
                    "binary_threshold": "auto"
                }
            },
            "operation": {
                "click_delay": 0.5,
                "input_delay": 0.2,
                "confirmation_timeout": 10,
                "failsafe_enabled": True
            },
            "database": {
                "path": "trading_data.db",
                "backup_interval": 3600,
                "max_records": 10000
            },
            "risk_control": {
                "max_daily_loss": 100,
                "max_consecutive_losses": 5,
                "max_position_size": 10,
                "stop_loss_percentage": 5,
                "take_profit_percentage": 10
            },
            "price_reading": {
                "recognition_areas": {
                    "latest_price": {"x": 200, "y": 150, "width": 100, "height": 30},
                    "real_time_value_change": {"x": 300, "y": 200, "width": 120, "height": 25},
                    "value_change": {"x": 300, "y": 230, "width": 120, "height": 25},
                    "market_status": {"x": 400, "y": 100, "width": 80, "height": 25}
                }
            },
            "order_execution": {
                "ui_elements": {
                    "buy_button": {"x": 100, "y": 500, "width": 80, "height": 30},
                    "sell_button": {"x": 200, "y": 500, "width": 80, "height": 30},
                    "price_input": {"x": 150, "y": 450, "width": 100, "height": 25},
                    "quantity_input": {"x": 150, "y": 480, "width": 100, "height": 25},
                    "confirm_order_button": {"x": 300, "y": 400, "width": 80, "height": 30},
                    "cancel_order_button": {"x": 400, "y": 400, "width": 80, "height": 30},
                    "transfer_button": {"x": 600, "y": 300, "width": 60, "height": 25},
                    "place_order_button": {"x": 250, "y": 520, "width": 100, "height": 35},
                    "agreement_checkbox": {"x": 150, "y": 550, "width": 20, "height": 20}
                }
            }
        }
    
    def get_config(self) -> Dict[str, Any]:
        """获取完整配置"""
        return self.config
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值"""
        try:
            keys = key.split('.')
            value = self.config
            
            for k in keys:
                if isinstance(value, dict) and k in value:
                    value = value[k]
                else:
                    return default
            
            return value
            
        except Exception as e:
            self.logger.error(f"获取配置失败 {key}: {e}")
            return default
    
    def set(self, key: str, value: Any) -> bool:
        """设置配置值"""
        try:
            keys = key.split('.')
            config = self.config
            
            # 导航到父级
            for k in keys[:-1]:
                if k not in config:
                    config[k] = {}
                config = config[k]
            
            # 设置值
            config[keys[-1]] = value
            
            # 保存配置
            return self.save_config(self.config)
            
        except Exception as e:
            self.logger.error(f"设置配置失败 {key}: {e}")
            return False
    
    def get_trading_config(self) -> Dict[str, Any]:
        """获取交易配置"""
        return self.get('trading', {})
    
    def get_ui_config(self) -> Dict[str, Any]:
        """获取界面配置"""
        return self.get('ui', {})
    
    def get_recognition_config(self) -> Dict[str, Any]:
        """获取识别配置"""
        return self.get('recognition', {})
    
    def get_operation_config(self) -> Dict[str, Any]:
        """获取操作配置"""
        return self.get('operation', {})
    
    def get_database_config(self) -> Dict[str, Any]:
        """获取数据库配置"""
        return self.get('database', {})
    
    def update_trading_config(self, config: Dict[str, Any]) -> bool:
        """更新交易配置"""
        try:
            current_config = self.get_trading_config()
            current_config.update(config)
            return self.set('trading', current_config)
        except Exception as e:
            self.logger.error(f"更新交易配置失败: {e}")
            return False
    
    def update_ui_config(self, config: Dict[str, Any]) -> bool:
        """更新界面配置"""
        try:
            current_config = self.get_ui_config()
            current_config.update(config)
            return self.set('ui', current_config)
        except Exception as e:
            self.logger.error(f"更新界面配置失败: {e}")
            return False
    
    def validate_config(self) -> bool:
        """验证配置"""
        try:
            required_keys = [
                'system.log_level',
                'trading.max_trades_per_day',
                'trading.max_loss_per_day',
                'recognition.confidence_threshold',
                'operation.click_delay'
            ]
            
            for key in required_keys:
                if self.get(key) is None:
                    self.logger.error(f"缺少必需配置: {key}")
                    return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"配置验证失败: {e}")
            return False
    
    def reset_to_default(self) -> bool:
        """重置为默认配置"""
        try:
            default_config = self.get_default_config()
            return self.save_config(default_config)
        except Exception as e:
            self.logger.error(f"重置配置失败: {e}")
            return False
    
    def export_config(self, file_path: str) -> bool:
        """导出配置"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=2)
            
            self.logger.info(f"配置已导出到: {file_path}")
            return True
            
        except Exception as e:
            self.logger.error(f"导出配置失败: {e}")
            return False
    
    def import_config(self, file_path: str) -> bool:
        """导入配置"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            return self.save_config(config)
            
        except Exception as e:
            self.logger.error(f"导入配置失败: {e}")
            return False
    
    def get_config_summary(self) -> Dict[str, Any]:
        """获取配置摘要"""
        try:
            return {
                'system': {
                    'log_level': self.get('system.log_level'),
                    'backup_interval': self.get('system.backup_interval')
                },
                'trading': {
                    'max_trades_per_day': self.get('trading.max_trades_per_day'),
                    'max_loss_per_day': self.get('trading.max_loss_per_day'),
                    'enable_risk_control': self.get('trading.enable_risk_control')
                },
                'recognition': {
                    'confidence_threshold': self.get('recognition.confidence_threshold'),
                    'enable_ocr': self.get('recognition.enable_ocr')
                },
                'operation': {
                    'click_delay': self.get('operation.click_delay'),
                    'enable_failsafe': self.get('operation.enable_failsafe')
                }
            }
        except Exception as e:
            self.logger.error(f"获取配置摘要失败: {e}")
            return {} 