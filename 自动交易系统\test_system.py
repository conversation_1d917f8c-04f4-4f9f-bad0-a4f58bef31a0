#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能交易系统测试脚本
用于验证所有依赖包和功能是否正常
"""

import sys
import traceback

def test_imports():
    """测试所有必要的导入"""
    print("测试导入模块...")
    
    try:
        import PyQt5
        print("✅ PyQt5: OK")
    except ImportError as e:
        print(f"❌ PyQt5: {e}")
        return False
    
    try:
        import cv2
        print("✅ OpenCV: OK")
    except ImportError as e:
        print(f"❌ OpenCV: {e}")
        return False
    
    try:
        import numpy
        print("✅ NumPy: OK")
    except ImportError as e:
        print(f"❌ NumPy: {e}")
        return False
    
    try:
        import pyautogui
        print("✅ PyAutoGUI: OK")
    except ImportError as e:
        print(f"❌ PyAutoGUI: {e}")
        return False
    
    try:
        import psutil
        print("✅ psutil: OK")
    except ImportError as e:
        print(f"❌ psutil: {e}")
        return False
    
    try:
        import pytesseract
        print("✅ pytesseract: OK")
    except ImportError as e:
        print("⚠️ pytesseract: 未安装，OCR功能将不可用")
    
    return True

def test_qt_functionality():
    """测试Qt功能"""
    print("\n测试Qt功能...")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from PyQt5.QtCore import QTimer
        
        app = QApplication([])
        print("✅ Qt应用程序创建: OK")
        
        # 测试定时器
        timer = QTimer()
        print("✅ Qt定时器: OK")
        
        app.quit()
        return True
        
    except Exception as e:
        print(f"❌ Qt功能测试失败: {e}")
        return False

def test_opencv_functionality():
    """测试OpenCV功能"""
    print("\n测试OpenCV功能...")
    
    try:
        import cv2
        import numpy as np
        
        # 创建测试图像
        test_image = np.zeros((100, 100, 3), dtype=np.uint8)
        print("✅ 图像创建: OK")
        
        # 测试颜色空间转换
        hsv = cv2.cvtColor(test_image, cv2.COLOR_BGR2HSV)
        print("✅ 颜色空间转换: OK")
        
        # 测试轮廓检测
        gray = cv2.cvtColor(test_image, cv2.COLOR_BGR2GRAY)
        contours, _ = cv2.findContours(gray, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        print("✅ 轮廓检测: OK")
        
        return True
        
    except Exception as e:
        print(f"❌ OpenCV功能测试失败: {e}")
        return False

def test_pyautogui_functionality():
    """测试PyAutoGUI功能"""
    print("\n测试PyAutoGUI功能...")
    
    try:
        import pyautogui
        
        # 获取屏幕尺寸
        size = pyautogui.size()
        print(f"✅ 屏幕尺寸: {size}")
        
        # 测试窗口检测
        windows = pyautogui.getAllWindows()
        print(f"✅ 窗口检测: 找到 {len(windows)} 个窗口")
        
        return True
        
    except Exception as e:
        print(f"❌ PyAutoGUI功能测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("智能交易系统环境测试")
    print("=" * 30)
    
    all_tests_passed = True
    
    # 测试导入
    if not test_imports():
        all_tests_passed = False
    
    # 测试Qt功能
    if not test_qt_functionality():
        all_tests_passed = False
    
    # 测试OpenCV功能
    if not test_opencv_functionality():
        all_tests_passed = False
    
    # 测试PyAutoGUI功能
    if not test_pyautogui_functionality():
        all_tests_passed = False
    
    print("\n" + "=" * 30)
    if all_tests_passed:
        print("🎉 所有测试通过！智能交易系统可以正常运行")
        print("现在可以运行: python smart_trading.py")
    else:
        print("❌ 部分测试失败，请检查依赖包安装")
        print("建议运行: pip install -i https://pypi.tuna.tsinghua.edu.cn/simple/ -r requirements_core.txt")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    try:
        main()
    except Exception as e:
        print(f"测试过程中发生错误: {e}")
        traceback.print_exc()
        input("\n按回车键退出...")
